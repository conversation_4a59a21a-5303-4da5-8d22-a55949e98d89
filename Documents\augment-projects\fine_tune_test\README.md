# Gemma 3 (4B) Vision 模型微調專案

這是一個將 Google Colab 筆記本轉換為本地端執行的 Gemma 3 Vision 模型微調專案。專案使用 Unsloth 框架對 Gemma 3 4B Vision 模型進行 LoRA 微調，用於 LaTeX OCR 任務（將手寫數學公式圖片轉換為 LaTeX 格式）。

## 專案概述

### 核心功能
- **模型微調**: 使用 Unsloth 框架對 Gemma 3 4B Vision 模型進行高效微調
- **LaTeX OCR**: 將手寫數學公式圖片轉換為 LaTeX 格式
- **LoRA 技術**: 使用 Low-Rank Adaptation 技術進行參數高效微調
- **本地端執行**: 完全在本地環境執行，無需依賴 Google Colab

### 技術特點
- 支援 4bit 量化載入，降低記憶體需求
- 使用 Unsloth 加速訓練，提升 2x 訓練速度
- 支援視覺語言模型的多模態微調
- 提供完整的推理介面

## 系統需求

### 硬體需求
- **GPU**: 建議使用 NVIDIA GPU（至少 8GB VRAM）
- **記憶體**: 至少 16GB RAM
- **儲存空間**: 至少 20GB 可用空間

### 軟體需求
- Python 3.8+
- CUDA 11.8+ (如果使用 GPU)
- Git

## 安裝指南

### 1. 克隆專案
```bash
git clone <repository-url>
cd fine_tune_test
```

### 2. 環境設定
```bash
# 執行環境設定腳本
python setup.py
```

這個腳本會：
- 檢查 Python 版本和 CUDA 環境
- 安裝所有必要的依賴套件
- 建立必要的目錄結構
- 測試套件匯入
- 建立範例設定檔

### 3. 手動安裝（如果自動安裝失敗）
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 模型微調
```bash
# 基本微調（使用預設參數）
python gemma3_vision_finetune.py

# 自訂參數微調
python gemma3_vision_finetune.py --max_steps 60 --output_dir my_outputs --save_path my_model
```

#### 微調參數說明
- `--max_steps`: 最大訓練步數（預設: 30）
- `--output_dir`: 訓練輸出目錄（預設: outputs）
- `--save_path`: 模型儲存路徑（預設: lora_model）
- `--skip_base_test`: 跳過基礎模型測試
- `--load_in_4bit`: 使用 4bit 量化載入（預設: True）

### 2. 模型推理

#### 單張圖片推理
```bash
python inference.py --image_path path/to/your/image.png
```

#### 批次推理
```bash
python inference.py --image_folder path/to/image/folder
```

#### 互動模式
```bash
python inference.py --interactive
```

#### 推理參數說明
- `--model_path`: 微調模型路徑（預設: lora_model）
- `--image_path`: 單張圖片路徑
- `--image_folder`: 圖片資料夾路徑
- `--instruction`: 推理指令（預設: "Write the LaTeX representation for this image."）
- `--interactive`: 啟用互動模式
- `--load_in_4bit`: 使用 4bit 量化載入

## 專案結構

```
fine_tune_test/
├── README.md                    # 專案說明文件
├── requirements.txt             # Python 依賴套件
├── setup.py                     # 環境設定腳本
├── config.py                    # 設定檔（自動生成）
├── gemma3_vision_finetune.py    # 主要微調腳本
├── inference.py                 # 推理腳本
├── outputs/                     # 訓練輸出目錄
├── lora_model/                  # 微調模型儲存目錄
├── data/                        # 資料目錄
└── logs/                        # 日誌目錄
```

## 資料集

專案使用 `unsloth/LaTeX_OCR` 資料集，包含：
- **訓練樣本**: 68,686 個手寫數學公式圖片
- **測試樣本**: 7,632 個手寫數學公式圖片
- **格式**: 圖片配對 LaTeX 文字

資料集會在首次執行時自動下載。

## 模型架構

### 基礎模型
- **模型**: Gemma 3 4B Vision (unsloth/gemma-3-4b-pt)
- **類型**: 視覺語言模型
- **參數量**: 約 43 億參數

### LoRA 設定
- **Rank (r)**: 16
- **Alpha**: 16
- **Dropout**: 0
- **目標模組**: all-linear
- **可訓練參數**: 約 3,850 萬（0.89% 的總參數）

## 訓練配置

### 預設訓練參數
- **批次大小**: 1
- **梯度累積步數**: 4
- **學習率**: 2e-4
- **最大步數**: 30
- **優化器**: AdamW (torch_fused)
- **學習率調度器**: cosine
- **最大序列長度**: 2048

### 記憶體優化
- 4bit 量化載入
- 梯度檢查點
- 混合精度訓練

## 效能指標

### 訓練效能
- **加速比**: 使用 Unsloth 可達到 2x 訓練加速
- **記憶體使用**: 4bit 量化可減少約 75% 記憶體使用
- **訓練時間**: 30 步約需 5-10 分鐘（Tesla T4）

### 推理效能
- **推理速度**: 單張圖片約 1-3 秒
- **準確性**: 微調後模型在 LaTeX OCR 任務上表現顯著提升

## 故障排除

### 常見問題

#### 1. CUDA 記憶體不足
```bash
# 使用更小的批次大小
python gemma3_vision_finetune.py --per_device_train_batch_size 1
```

#### 2. 套件安裝失敗
```bash
# 手動安裝 Unsloth
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
```

#### 3. 模型下載失敗
```bash
# 設定 Hugging Face 快取目錄
export HF_HOME=/path/to/large/disk
```

### 日誌檢查
訓練和推理過程中的詳細日誌會輸出到控制台，可以用於診斷問題。

## 進階使用

### 自訂資料集
如果要使用自己的資料集，請修改 `gemma3_vision_finetune.py` 中的 `load_and_prepare_dataset()` 函數。

### 調整 LoRA 參數
可以在 `setup_lora_adapters()` 函數中調整 LoRA 參數以獲得更好的效能。

### 匯出為其他格式
```python
# 匯出為 float16 格式
model.save_pretrained_merged("model_16bit", processor)

# 匯出到 Hugging Face Hub
model.push_to_hub_merged("your_username/model_name", processor, token="your_token")
```

## 授權

本專案基於原始 Unsloth 範例進行修改，遵循相應的開源授權。

## 貢獻

歡迎提交 Issue 和 Pull Request 來改進這個專案。

## 聯絡資訊

如有問題，請透過以下方式聯絡：
- GitHub Issues
- [Unsloth Discord](https://discord.gg/unsloth)

## 致謝

- [Unsloth AI](https://unsloth.ai/) - 提供高效的模型微調框架
- [Google](https://ai.google.dev/) - Gemma 模型
- [Hugging Face](https://huggingface.co/) - 模型和資料集託管
