#!/usr/bin/env python3
"""
Gemma 3 (4B) Vision 模型微調腳本
用於 LaTeX OCR 任務的本地端執行版本

原始 Colab 筆記本轉換為本地端執行腳本
"""

import os
import torch
from datasets import load_dataset
from unsloth import FastVisionModel, get_chat_template
from unsloth.trainer import UnslothVisionDataCollator
from trl import SFTTrainer, SFTConfig
from transformers import TextStreamer
from IPython.display import display, Math
import argparse
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_environment():
    """設定執行環境"""
    # 檢查 CUDA 可用性
    if not torch.cuda.is_available():
        logger.warning("CUDA 不可用，將使用 CPU 執行（速度會很慢）")
        return "cpu"
    else:
        logger.info(f"CUDA 可用，GPU 數量: {torch.cuda.device_count()}")
        return "cuda"

def load_model_and_processor(model_name="unsloth/gemma-3-4b-pt", load_in_4bit=True):
    """載入模型和處理器"""
    logger.info(f"載入模型: {model_name}")
    
    model, processor = FastVisionModel.from_pretrained(
        model_name,
        load_in_4bit=load_in_4bit,
        use_gradient_checkpointing="unsloth",
    )
    
    logger.info("模型載入完成")
    return model, processor

def setup_lora_adapters(model):
    """設定 LoRA 適配器"""
    logger.info("設定 LoRA 適配器")
    
    model = FastVisionModel.get_peft_model(
        model,
        finetune_vision_layers=True,
        finetune_language_layers=True,
        finetune_attention_modules=True,
        finetune_mlp_modules=True,
        r=16,
        lora_alpha=16,
        lora_dropout=0,
        bias="none",
        random_state=3407,
        use_rslora=False,
        loftq_config=None,
        target_modules="all-linear",
        modules_to_save=[
            "lm_head",
            "embed_tokens",
        ],
    )
    
    logger.info("LoRA 適配器設定完成")
    return model

def load_and_prepare_dataset():
    """載入並準備資料集"""
    logger.info("載入 LaTeX OCR 資料集")
    
    dataset = load_dataset("unsloth/LaTeX_OCR", split="train")
    logger.info(f"資料集載入完成，共 {len(dataset)} 個樣本")
    
    return dataset

def convert_to_conversation(sample, instruction="Write the LaTeX representation for this image."):
    """將資料集轉換為對話格式"""
    conversation = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": instruction},
                {"type": "image", "image": sample["image"]},
            ],
        },
        {"role": "assistant", "content": [{"type": "text", "text": sample["text"]}]},
    ]
    return {"messages": conversation}

def prepare_dataset(dataset):
    """準備訓練資料集"""
    logger.info("準備訓練資料集")
    
    converted_dataset = [convert_to_conversation(sample) for sample in dataset]
    logger.info("資料集轉換完成")
    
    return converted_dataset

def setup_chat_template(processor):
    """設定聊天模板"""
    logger.info("設定 Gemma-3 聊天模板")
    
    processor = get_chat_template(processor, "gemma-3")
    logger.info("聊天模板設定完成")
    
    return processor

def test_base_model(model, processor, dataset):
    """測試基礎模型效能"""
    logger.info("測試基礎模型效能")
    
    FastVisionModel.for_inference(model)
    
    image = dataset[2]["image"]
    instruction = "Write the LaTeX representation for this image."
    
    messages = [
        {
            "role": "user",
            "content": [{"type": "image"}, {"type": "text", "text": instruction}],
        }
    ]
    
    input_text = processor.apply_chat_template(messages, add_generation_prompt=True)
    inputs = processor(
        image,
        input_text,
        add_special_tokens=False,
        return_tensors="pt",
    ).to("cuda" if torch.cuda.is_available() else "cpu")
    
    text_streamer = TextStreamer(processor, skip_prompt=True)
    logger.info("基礎模型輸出:")
    
    result = model.generate(
        **inputs, 
        streamer=text_streamer, 
        max_new_tokens=128,
        use_cache=True, 
        temperature=1.0, 
        top_p=0.95, 
        top_k=64
    )
    
    return result

def train_model(model, processor, converted_dataset, max_steps=30, output_dir="outputs"):
    """訓練模型"""
    logger.info("開始模型訓練")
    
    FastVisionModel.for_training(model)
    
    trainer = SFTTrainer(
        model=model,
        train_dataset=converted_dataset,
        processing_class=processor.tokenizer,
        data_collator=UnslothVisionDataCollator(model, processor),
        args=SFTConfig(
            per_device_train_batch_size=1,
            gradient_accumulation_steps=4,
            gradient_checkpointing=True,
            gradient_checkpointing_kwargs={"use_reentrant": False},
            max_grad_norm=0.3,
            warmup_ratio=0.03,
            max_steps=max_steps,
            learning_rate=2e-4,
            logging_steps=1,
            save_strategy="steps",
            optim="adamw_torch_fused",
            weight_decay=0.01,
            lr_scheduler_type="cosine",
            seed=3407,
            output_dir=output_dir,
            report_to="none",
            remove_unused_columns=False,
            dataset_text_field="",
            dataset_kwargs={"skip_prepare_dataset": True},
            max_length=2048,
        )
    )
    
    # 顯示記憶體使用情況
    if torch.cuda.is_available():
        gpu_stats = torch.cuda.get_device_properties(0)
        start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
        max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
        logger.info(f"GPU = {gpu_stats.name}. 最大記憶體 = {max_memory} GB.")
        logger.info(f"{start_gpu_memory} GB 記憶體已保留.")
    
    # 開始訓練
    trainer.train()
    logger.info("模型訓練完成")
    
    return trainer

def test_finetuned_model(model, processor, dataset):
    """測試微調後的模型"""
    logger.info("測試微調後的模型效能")
    
    FastVisionModel.for_inference(model)
    
    image = dataset[10]["image"]
    instruction = "Write the LaTeX representation for this image."
    
    messages = [
        {
            "role": "user",
            "content": [{"type": "image"}, {"type": "text", "text": instruction}],
        }
    ]
    
    input_text = processor.apply_chat_template(messages, add_generation_prompt=True)
    inputs = processor(
        image,
        input_text,
        add_special_tokens=False,
        return_tensors="pt",
    ).to("cuda" if torch.cuda.is_available() else "cpu")
    
    text_streamer = TextStreamer(processor, skip_prompt=True)
    logger.info("微調後模型輸出:")
    
    result = model.generate(
        **inputs, 
        streamer=text_streamer, 
        max_new_tokens=128,
        use_cache=True, 
        temperature=1.0, 
        top_p=0.95, 
        top_k=64
    )
    
    return result

def save_model(model, processor, save_path="lora_model"):
    """儲存模型"""
    logger.info(f"儲存模型到 {save_path}")
    
    model.save_pretrained(save_path)
    processor.save_pretrained(save_path)
    
    logger.info("模型儲存完成")

def main():
    parser = argparse.ArgumentParser(description="Gemma 3 Vision 模型微調")
    parser.add_argument("--max_steps", type=int, default=30, help="最大訓練步數")
    parser.add_argument("--output_dir", type=str, default="outputs", help="輸出目錄")
    parser.add_argument("--save_path", type=str, default="lora_model", help="模型儲存路徑")
    parser.add_argument("--skip_base_test", action="store_true", help="跳過基礎模型測試")
    parser.add_argument("--load_in_4bit", action="store_true", default=True, help="使用 4bit 量化載入")
    
    args = parser.parse_args()
    
    # 設定環境
    device = setup_environment()
    
    # 載入模型和處理器
    model, processor = load_model_and_processor(load_in_4bit=args.load_in_4bit)
    
    # 設定 LoRA 適配器
    model = setup_lora_adapters(model)
    
    # 載入資料集
    dataset = load_and_prepare_dataset()
    
    # 準備資料集
    converted_dataset = prepare_dataset(dataset)
    
    # 設定聊天模板
    processor = setup_chat_template(processor)
    
    # 測試基礎模型（可選）
    if not args.skip_base_test:
        test_base_model(model, processor, dataset)
    
    # 訓練模型
    trainer = train_model(model, processor, converted_dataset, args.max_steps, args.output_dir)
    
    # 測試微調後的模型
    test_finetuned_model(model, processor, dataset)
    
    # 儲存模型
    save_model(model, processor, args.save_path)
    
    logger.info("所有任務完成！")

if __name__ == "__main__":
    main()
