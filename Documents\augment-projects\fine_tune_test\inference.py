#!/usr/bin/env python3
"""
Gemma 3 Vision 模型推理腳本
用於載入微調後的模型進行推理
"""

import os
import torch
from PIL import Image
from unsloth import FastVisionModel
from transformers import TextStreamer
import argparse
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_finetuned_model(model_path, load_in_4bit=True):
    """載入微調後的模型"""
    logger.info(f"載入微調模型: {model_path}")
    
    model, processor = FastVisionModel.from_pretrained(
        model_path,
        load_in_4bit=load_in_4bit,
    )
    
    FastVisionModel.for_inference(model)
    logger.info("模型載入完成")
    
    return model, processor

def inference_on_image(model, processor, image_path, instruction="Write the LaTeX representation for this image."):
    """對單張圖片進行推理"""
    logger.info(f"對圖片進行推理: {image_path}")
    
    # 載入圖片
    image = Image.open(image_path).convert("RGB")
    
    # 準備訊息
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "image"},
                {"type": "text", "text": instruction}
            ],
        }
    ]
    
    # 應用聊天模板
    input_text = processor.apply_chat_template(messages, add_generation_prompt=True)
    
    # 處理輸入
    inputs = processor(
        image,
        input_text,
        add_special_tokens=False,
        return_tensors="pt",
    ).to("cuda" if torch.cuda.is_available() else "cpu")
    
    # 設定文字串流器
    text_streamer = TextStreamer(processor.tokenizer, skip_prompt=True)
    
    logger.info("模型輸出:")
    
    # 生成結果
    result = model.generate(
        **inputs,
        streamer=text_streamer,
        max_new_tokens=128,
        use_cache=True,
        temperature=1.0,
        top_p=0.95,
        top_k=64
    )
    
    return result

def batch_inference(model, processor, image_folder, instruction="Write the LaTeX representation for this image."):
    """批次推理多張圖片"""
    logger.info(f"批次推理資料夾: {image_folder}")
    
    # 支援的圖片格式
    supported_formats = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff')
    
    # 獲取所有圖片檔案
    image_files = [f for f in os.listdir(image_folder) 
                   if f.lower().endswith(supported_formats)]
    
    if not image_files:
        logger.warning(f"在 {image_folder} 中沒有找到支援的圖片檔案")
        return
    
    logger.info(f"找到 {len(image_files)} 張圖片")
    
    results = []
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
        logger.info(f"\n處理圖片: {image_file}")
        
        try:
            result = inference_on_image(model, processor, image_path, instruction)
            results.append((image_file, result))
        except Exception as e:
            logger.error(f"處理 {image_file} 時發生錯誤: {e}")
            continue
    
    return results

def interactive_mode(model, processor):
    """互動模式"""
    logger.info("進入互動模式，輸入 'quit' 退出")
    
    while True:
        image_path = input("\n請輸入圖片路徑 (或 'quit' 退出): ").strip()
        
        if image_path.lower() == 'quit':
            break
        
        if not os.path.exists(image_path):
            print(f"檔案不存在: {image_path}")
            continue
        
        try:
            instruction = input("請輸入指令 (直接按 Enter 使用預設指令): ").strip()
            if not instruction:
                instruction = "Write the LaTeX representation for this image."
            
            inference_on_image(model, processor, image_path, instruction)
        except Exception as e:
            logger.error(f"推理時發生錯誤: {e}")

def main():
    parser = argparse.ArgumentParser(description="Gemma 3 Vision 模型推理")
    parser.add_argument("--model_path", type=str, default="lora_model", help="模型路徑")
    parser.add_argument("--image_path", type=str, help="單張圖片路徑")
    parser.add_argument("--image_folder", type=str, help="圖片資料夾路徑（批次推理）")
    parser.add_argument("--instruction", type=str, 
                       default="Write the LaTeX representation for this image.",
                       help="推理指令")
    parser.add_argument("--interactive", action="store_true", help="互動模式")
    parser.add_argument("--load_in_4bit", action="store_true", default=True, help="使用 4bit 量化載入")
    
    args = parser.parse_args()
    
    # 載入模型
    model, processor = load_finetuned_model(args.model_path, args.load_in_4bit)
    
    if args.interactive:
        # 互動模式
        interactive_mode(model, processor)
    elif args.image_path:
        # 單張圖片推理
        inference_on_image(model, processor, args.image_path, args.instruction)
    elif args.image_folder:
        # 批次推理
        batch_inference(model, processor, args.image_folder, args.instruction)
    else:
        logger.error("請指定 --image_path、--image_folder 或使用 --interactive 模式")

if __name__ == "__main__":
    main()
