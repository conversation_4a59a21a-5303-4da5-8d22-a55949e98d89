#!/usr/bin/env python3
"""
快速開始腳本
一鍵執行完整的微調流程
"""

import os
import sys
import subprocess
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description):
    """執行命令並處理錯誤"""
    logger.info(f"執行: {description}")
    logger.info(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✓ {description} 完成")
        if result.stdout:
            logger.info(f"輸出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ {description} 失敗")
        logger.error(f"錯誤: {e.stderr}")
        return False

def check_prerequisites():
    """檢查先決條件"""
    logger.info("檢查先決條件...")
    
    # 檢查 Python 版本
    if sys.version_info < (3, 8):
        logger.error("需要 Python 3.8 或更高版本")
        return False
    
    # 檢查必要檔案
    required_files = [
        "requirements.txt",
        "gemma3_vision_finetune.py",
        "inference.py",
        "setup.py"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            logger.error(f"找不到必要檔案: {file}")
            return False
    
    logger.info("✓ 先決條件檢查通過")
    return True

def setup_environment():
    """設定環境"""
    logger.info("設定環境...")
    
    if not run_command("python setup.py", "環境設定"):
        return False
    
    return True

def run_training():
    """執行訓練"""
    logger.info("開始模型微調...")
    
    # 使用較少的步數進行快速測試
    command = "python gemma3_vision_finetune.py --max_steps 10"
    
    if not run_command(command, "模型微調"):
        return False
    
    return True

def test_inference():
    """測試推理"""
    logger.info("測試推理功能...")
    
    # 檢查模型是否存在
    if not os.path.exists("lora_model"):
        logger.error("找不到微調後的模型")
        return False
    
    logger.info("模型已準備好進行推理")
    logger.info("您可以使用以下命令進行推理:")
    logger.info("python inference.py --interactive")
    
    return True

def main():
    """主函數"""
    logger.info("=== Gemma 3 Vision 微調快速開始 ===")
    
    # 檢查先決條件
    if not check_prerequisites():
        logger.error("先決條件檢查失敗，請檢查環境")
        sys.exit(1)
    
    # 設定環境
    if not setup_environment():
        logger.error("環境設定失敗")
        sys.exit(1)
    
    # 執行訓練
    if not run_training():
        logger.error("模型微調失敗")
        sys.exit(1)
    
    # 測試推理
    if not test_inference():
        logger.error("推理測試失敗")
        sys.exit(1)
    
    logger.info("=== 快速開始完成！===")
    logger.info("您現在可以:")
    logger.info("1. 使用 'python inference.py --interactive' 進行互動式推理")
    logger.info("2. 使用 'python inference.py --image_path <path>' 對單張圖片推理")
    logger.info("3. 使用 'python gemma3_vision_finetune.py --max_steps 60' 進行更長時間的訓練")

if __name__ == "__main__":
    main()
