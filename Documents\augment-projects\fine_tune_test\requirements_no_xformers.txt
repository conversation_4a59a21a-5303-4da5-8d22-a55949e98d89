# Gemma3 270M 微調專案依賴套件
# 適用於本地端 GPU 環境

# 核心深度學習框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Transformers 和相關套件
transformers>=4.40.0
tokenizers>=0.15.0
accelerate>=0.20.0
peft>=0.10.0
trl>=0.8.0

# 資料處理
datasets>=2.18.0
numpy>=1.24.0
pandas>=2.0.0

# Unsloth 相關 (將透過 pip install 安裝)
# unsloth - 需要根據 CUDA 版本選擇安裝方式

# 量化和優化
bitsandbytes>=0.41.0
# xformers>=0.0.20

# 其他工具
sentencepiece>=0.1.99
protobuf>=4.21.0
huggingface-hub>=0.20.0
safetensors>=0.4.0

# 開發和除錯工具
jupyter>=1.0.0
notebook>=6.5.0
ipywidgets>=8.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
tqdm>=4.65.0

# 可選：監控和記錄
wandb>=0.15.0
tensorboard>=2.13.0

# 系統工具
psutil>=5.9.0
GPUtil>=1.4.0
