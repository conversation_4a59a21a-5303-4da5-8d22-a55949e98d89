#!/usr/bin/env python3
"""
環境設定腳本
用於檢查和設定 Gemma 3 Vision 微調環境
"""

import subprocess
import sys
import os
import torch
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_python_version():
    """檢查 Python 版本"""
    version = sys.version_info
    logger.info(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要 Python 3.8 或更高版本")
        return False
    
    return True

def check_cuda():
    """檢查 CUDA 環境"""
    logger.info("檢查 CUDA 環境...")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA 可用，版本: {torch.version.cuda}")
        logger.info(f"GPU 數量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_props = torch.cuda.get_device_properties(i)
            memory_gb = gpu_props.total_memory / 1024**3
            logger.info(f"GPU {i}: {gpu_props.name}, 記憶體: {memory_gb:.1f} GB")
        
        return True
    else:
        logger.warning("CUDA 不可用，將使用 CPU（速度會很慢）")
        return False

def install_requirements():
    """安裝必要套件"""
    logger.info("安裝必要套件...")
    
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        logger.error(f"找不到 {requirements_file}")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ])
        logger.info("套件安裝完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"套件安裝失敗: {e}")
        return False

def create_directories():
    """建立必要的目錄"""
    directories = [
        "outputs",
        "lora_model",
        "data",
        "logs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"建立目錄: {directory}")

def test_imports():
    """測試重要套件的匯入"""
    logger.info("測試套件匯入...")
    
    test_packages = [
        "torch",
        "transformers",
        "datasets",
        "unsloth",
        "trl",
        "PIL",
        "numpy"
    ]
    
    failed_imports = []
    
    for package in test_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError as e:
            logger.error(f"✗ {package}: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        logger.error(f"以下套件匯入失敗: {failed_imports}")
        return False
    
    logger.info("所有套件匯入成功")
    return True

def check_disk_space():
    """檢查磁碟空間"""
    logger.info("檢查磁碟空間...")
    
    # 獲取當前目錄的磁碟使用情況
    statvfs = os.statvfs('.')
    free_space_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)
    
    logger.info(f"可用磁碟空間: {free_space_gb:.1f} GB")
    
    # 建議至少需要 20GB 空間
    if free_space_gb < 20:
        logger.warning("磁碟空間不足，建議至少有 20GB 可用空間")
        return False
    
    return True

def create_sample_config():
    """建立範例設定檔"""
    config_content = """# Gemma 3 Vision 微調設定檔

# 模型設定
MODEL_NAME = "unsloth/gemma-3-4b-pt"
LOAD_IN_4BIT = True

# 訓練設定
MAX_STEPS = 30
BATCH_SIZE = 1
GRADIENT_ACCUMULATION_STEPS = 4
LEARNING_RATE = 2e-4

# LoRA 設定
LORA_R = 16
LORA_ALPHA = 16
LORA_DROPOUT = 0

# 路徑設定
OUTPUT_DIR = "outputs"
SAVE_PATH = "lora_model"

# 資料集設定
DATASET_NAME = "unsloth/LaTeX_OCR"
INSTRUCTION = "Write the LaTeX representation for this image."
"""
    
    with open("config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    logger.info("建立範例設定檔: config.py")

def main():
    """主函數"""
    logger.info("開始環境設定檢查...")
    
    # 檢查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 檢查 CUDA
    cuda_available = check_cuda()
    
    # 檢查磁碟空間
    if not check_disk_space():
        logger.warning("磁碟空間不足，但仍可繼續")
    
    # 建立目錄
    create_directories()
    
    # 安裝套件
    if not install_requirements():
        logger.error("套件安裝失敗，請手動安裝")
        sys.exit(1)
    
    # 測試匯入
    if not test_imports():
        logger.error("套件匯入測試失敗")
        sys.exit(1)
    
    # 建立範例設定檔
    create_sample_config()
    
    logger.info("環境設定完成！")
    
    if cuda_available:
        logger.info("您的環境已準備好進行 GPU 加速訓練")
    else:
        logger.info("您的環境將使用 CPU 進行訓練（速度較慢）")
    
    logger.info("您現在可以執行以下命令開始訓練:")
    logger.info("python gemma3_vision_finetune.py")

if __name__ == "__main__":
    main()
